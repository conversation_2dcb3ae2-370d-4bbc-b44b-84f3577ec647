{"name": "debounce-fn", "version": "5.1.2", "description": "Debounce a function", "license": "MIT", "repository": "sindresorhus/debounce-fn", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["debounce", "function", "debouncer", "fn", "func", "throttle", "delay", "invoked"], "dependencies": {"mimic-fn": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "delay": "^5.0.0", "tsd": "^0.19.1", "xo": "^0.47.0"}}