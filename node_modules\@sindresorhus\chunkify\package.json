{"name": "@sindresorhus/chunkify", "version": "1.0.0", "description": "Split an iterable into evenly sized chunks", "license": "MIT", "repository": "sindresorhus/chunkify", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["chunkify", "chunk", "chunks", "chunked", "chunking", "array", "iterable", "iterator", "generator", "set", "map", "parts", "split", "size", "partition", "divide", "segment", "batch", "slice", "subarray", "group", "allocate", "dissect", "segregate", "separate", "section"], "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}}