declare const DEFAULT_ENCODING = "utf8";
declare const DEFAULT_FILE_MODE = 438;
declare const DEFAULT_FOLDER_MODE = 511;
declare const DEFAULT_READ_OPTIONS: {};
declare const DEFAULT_WRITE_OPTIONS: {};
declare const DEFAULT_USER_UID: number;
declare const DEFAULT_USER_GID: number;
declare const DEFAULT_TIMEOUT_ASYNC = 7500;
declare const DEFAULT_TIMEOUT_SYNC = 1000;
declare const IS_POSIX: boolean;
declare const IS_USER_ROOT: boolean;
declare const LIMIT_BASENAME_LENGTH = 128;
declare const LIMIT_FILES_DESCRIPTORS = 10000;
declare const NOOP: () => void;
export { DEFAULT_ENCODING, DEFAULT_FILE_MODE, DEFAULT_FOLDER_MODE, DEFAULT_READ_OPTIONS, DEFAULT_WRITE_OPTIONS, DEFAULT_USER_UID, DEFAULT_USER_GID, DEFAULT_TIMEOUT_ASYNC, DEFAULT_TIMEOUT_SYNC, IS_POSIX, IS_USER_ROOT, LIMIT_BASENAME_LENGTH, LIMIT_FILES_DESCRIPTORS, NOOP };
