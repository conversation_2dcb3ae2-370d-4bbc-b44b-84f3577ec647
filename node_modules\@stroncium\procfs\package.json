{"name": "@stroncium/procfs", "version": "1.2.1", "description": "Zero dependency library for reading and parsing various files from `procfs` for Node.js, implemented in pure JS.", "main": "index.js", "files": ["lib/**"], "scripts": {"build:api-xml": "haxe -D use_rtti_doc -cp haxe -xml ./builder/.tmp/api.xml procfs.Procfs", "build:clean-api-xml": "rm ./builder/.tmp/api.xml ./builder/.tmp/Main.n", "build:docs-asserts": "cd ./builder/.tmp && SRC=./api.xml DST_API_MD=../../API.md DST_ASSERTS=../../test/fixtures/type/asserts.js haxe -cp ../ -x Main", "build": "npm run build:api-xml && npm run build:docs-asserts && npm run build:clean-api-xml", "coverage": "codecov", "fulltest": "npm run build && npm run test", "test": "nyc ava && xo"}, "engines": {"node": ">=8"}, "author": "<PERSON><PERSON> <<EMAIL>>", "keywords": ["proc", "procfs", "process", "uptime", "stat", "statm", "status", "mountinfo", "cpuinfo", "loadavg", "meminfo", "diskstats", "io", "devices", "filesystems", "partitions", "swaps", "autogroup", "cgroups", "cmdline", "comm", "cpuset", "cwd", "environ", "exe", "fd", "gid_map", "limits", "oom_score", "personality", "setgroups", "timerslack_ns", "uid_map", "cmdline", "config", "version", "nodejs", "linux"], "license": "CC0-1.0", "homepage": "https://github.com/stroncium/nodejs-procfs", "bugs": {"url": "https://github.com/stroncium/nodejs-procfs/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/stroncium/nodejs-procfs.git"}, "devDependencies": {"ava": "^1.4.1", "codecov": "^3.5.0", "haxe": "^5.0.10", "joi": "^14.3.1", "nyc": "^14.1.1", "xo": "^0.24.0"}, "xo": {"rules": {"comma-dangle": ["error", "always-multiline"], "prefer-const": ["off"], "padding-line-between-statements": ["off"], "radix": ["off"], "no-return-assign": ["off"], "no-control-regex": ["off"], "capitalized-comments": ["off"], "spaced-comment": ["off"], "unicorn/filename-case": ["off"], "unicorn/no-hex-escape": ["off"], "prefer-destructuring": ["off"], "operator-assignment": ["off"]}}}