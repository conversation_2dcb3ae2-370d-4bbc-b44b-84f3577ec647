{"name": "dot-prop", "version": "8.0.2", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": "sindresorhus/dot-prop", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsc", "bench": "node benchmark.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^3.8.0"}, "devDependencies": {"ava": "^5.2.0", "benchmark": "^2.1.4", "expect-type": "^0.15.0", "typescript": "^5.0.4", "xo": "^0.54.1"}}